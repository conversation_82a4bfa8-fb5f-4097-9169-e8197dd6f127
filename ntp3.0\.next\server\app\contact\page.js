/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/contact/page";
exports.ids = ["app/contact/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contact/page.tsx */ \"(rsc)/./src/app/contact/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'contact',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/contact/page\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZjb250YWN0JTJGcGFnZSZwYWdlPSUyRmNvbnRhY3QlMkZwYWdlJmFwcFBhdGhzPSUyRmNvbnRhY3QlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGY29udGFjdCUyRnBhZ2UudHN4JmFwcERpcj1EJTNBJTVDUHJvamVjdHMlNUNXZWIlMjBGaWxlcyU1Q25lYXJ0ZWtwb2QlNUNuZWFydGVrcG9kJTIwMy4wJTVDbnRwMy4wJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDUHJvamVjdHMlNUNXZWIlMjBGaWxlcyU1Q25lYXJ0ZWtwb2QlNUNuZWFydGVrcG9kJTIwMy4wJTVDbnRwMy4wJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQXNIO0FBQzVJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixnS0FBNkg7QUFHL0k7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQThRO0FBQ2xUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBOFE7QUFDbFQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxzcmNcXFxcYXBwXFxcXGNvbnRhY3RcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2NvbnRhY3QnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcbmVhcnRla3BvZCAzLjBcXFxcbnRwMy4wXFxcXHNyY1xcXFxhcHBcXFxcY29udGFjdFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxzcmNcXFxcYXBwXFxcXGNvbnRhY3RcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL2NvbnRhY3QvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvY29udGFjdFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contact/page.tsx */ \"(rsc)/./src/app/contact/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjb250YWN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcbmVhcnRla3BvZCAzLjBcXFxcbnRwMy4wXFxcXHNyY1xcXFxhcHBcXFxcY29udGFjdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"40x39\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxuZWFydGVrcG9kIDMuMFxcbnRwMy4wXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiNDB4MzlcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Web Files\\neartekpod\\neartekpod 3.0\\ntp3.0\\src\\app\\contact\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dda28838a735\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXFdlYiBGaWxlc1xcbmVhcnRla3BvZFxcbmVhcnRla3BvZCAzLjBcXG50cDMuMFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZGRhMjg4MzhhNzM1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcV2ViIEZpbGVzXFxuZWFydGVrcG9kXFxuZWFydGVrcG9kIDMuMFxcbnRwMy4wXFxzcmNcXGFwcFxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcclxuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xyXG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XHJcblxyXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XHJcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LXNhbnNcIixcclxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcclxufSk7XHJcblxyXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcclxuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxyXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxyXG59KTtcclxuXHJcbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XHJcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXHJcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IFJlYWRvbmx5PHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59Pikge1xyXG4gIHJldHVybiAoXHJcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cclxuICAgICAgPGJvZHlcclxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cclxuICAgICAgPlxyXG4gICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgPC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDUHJvamVjdHMlNUMlNUNXZWIlMjBGaWxlcyU1QyU1Q25lYXJ0ZWtwb2QlNUMlNUNuZWFydGVrcG9kJTIwMy4wJTVDJTVDbnRwMy4wJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDY2xpZW50LXNlZ21lbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDV2ViJTIwRmlsZXMlNUMlNUNuZWFydGVrcG9kJTVDJTVDbmVhcnRla3BvZCUyMDMuMCU1QyU1Q250cDMuMCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNodHRwLWFjY2Vzcy1mYWxsYmFjayU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q21ldGFkYXRhLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvT0FBOEo7QUFDOUo7QUFDQSwwT0FBaUs7QUFDaks7QUFDQSwwT0FBaUs7QUFDaks7QUFDQSxvUkFBdUw7QUFDdkw7QUFDQSx3T0FBZ0s7QUFDaEs7QUFDQSw0UEFBMks7QUFDM0s7QUFDQSxrUUFBOEs7QUFDOUs7QUFDQSxzUUFBK0siLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGNsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcbmVhcnRla3BvZCAzLjBcXFxcbnRwMy4wXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGh0dHAtYWNjZXNzLWZhbGxiYWNrXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxQcm9qZWN0c1xcXFxXZWIgRmlsZXNcXFxcbmVhcnRla3BvZFxcXFxuZWFydGVrcG9kIDMuMFxcXFxudHAzLjBcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxhc3luYy1tZXRhZGF0YS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcbmVhcnRla3BvZCAzLjBcXFxcbnRwMy4wXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXFdlYiBGaWxlc1xcXFxuZWFydGVrcG9kXFxcXG5lYXJ0ZWtwb2QgMy4wXFxcXG50cDMuMFxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/contact/page.tsx */ \"(ssr)/./src/app/contact/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1dlYiUyMEZpbGVzJTVDJTVDbmVhcnRla3BvZCU1QyU1Q25lYXJ0ZWtwb2QlMjAzLjAlNUMlNUNudHAzLjAlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNjb250YWN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUE2SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcUHJvamVjdHNcXFxcV2ViIEZpbGVzXFxcXG5lYXJ0ZWtwb2RcXFxcbmVhcnRla3BvZCAzLjBcXFxcbnRwMy4wXFxcXHNyY1xcXFxhcHBcXFxcY29udGFjdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CWeb%20Files%5C%5Cneartekpod%5C%5Cneartekpod%203.0%5C%5Cntp3.0%5C%5Csrc%5C%5Capp%5C%5Ccontact%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/components/navigation-header.tsx":
/*!**************************************************!*\
  !*** ./src/app/components/navigation-header.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Component)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,FileText,Home,Menu,User,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Component() {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const toggleMobileMenu = ()=>{\n        setIsMobileMenuOpen(!isMobileMenuOpen);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#ffffff] w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"container flex items-center justify-between px-4 md:px-8 max-w-7xl mx-auto\",\n                style: {\n                    height: '72px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                src: \"/ntp-logo.png\",\n                                alt: \"NearTekPod Logo\",\n                                width: 219,\n                                height: 63,\n                                priority: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `flex items-center space-x-2 px-4 text-[#1c274c] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === \"/\" ? \" bg-[#a7dfff]\" : \" bg-[#e7eaee]\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/services\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === \"/services\" ? \" bg-[#a7dfff]\" : \" bg-[#e7eaee]\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === \"/about\" ? \" bg-[#a7dfff]\" : \" bg-[#e7eaee]\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"About Us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/career\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === \"/career\" ? \" bg-[#a7dfff]\" : \" bg-[#e7eaee]\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Career\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/blog\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === \"/blog\" ? \" bg-[#a7dfff]\" : \" bg-[#e7eaee]\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Blog\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `px-6 border-1 border-[#00a2ff] text-[#00a2ff] rounded-full font-semibold hover:bg-[#a7dfff] hover:text-[#1c274c] transition-colors` + (pathname === \"/contact\" ? \" bg-[#a7dfff]\" : \" bg-transparent\"),\n                                    style: {\n                                        height: '40px',\n                                        fontSize: '12px',\n                                        fontFamily: 'Montserrat, sans-serif',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"CONTACT US\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"md:hidden p-2 text-[#1c274c]\",\n                        onClick: toggleMobileMenu,\n                        \"aria-label\": \"Toggle mobile menu\",\n                        children: isMobileMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 31\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"w-6 h-6\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 59\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden fixed inset-0 bg-black bg-opacity-50 z-40\",\n                onClick: toggleMobileMenu\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `md:hidden fixed top-0 right-0 h-full w-80 bg-[#ffffff] shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${isMobileMenuOpen ? \"translate-x-0\" : \"translate-x-full\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/ntp-logo.png\",\n                                        alt: \"NearTekPod Logo\",\n                                        width: 138,\n                                        height: 40,\n                                        priority: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleMobileMenu,\n                                    className: \"p-2 text-[#1c274c]\",\n                                    \"aria-label\": \"Close mobile menu\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 bg-[#a7dfff] text-[#1c274c] rounded-lg font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5 cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Home\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/services\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-5 h-5 cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Services\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/about\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-5 h-5 cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"About Us\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/career\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Career\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/blog\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_FileText_Home_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-5 h-5 cursor-pointer\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Blog\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"w-full mt-6 px-6 py-3 border-2 border-[#00a2ff] text-[#00a2ff] rounded-lg font-semibold hover:bg-[#00a2ff] hover:text-[#ffffff] transition-colors cursor-pointer\",\n                                    children: \"CONTACT US\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\components\\\\navigation-header.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/components/navigation-header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/navigation-header */ \"(ssr)/./src/app/components/navigation-header.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_Email_emailService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/Email/emailService */ \"(ssr)/./src/app/services/Email/emailService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ContactPage() {\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        First_name: \"\",\n        Last_name: \"\",\n        Email: \"\",\n        Company_name: \"\",\n        Country: \"\",\n        Message: \"\"\n    });\n    const FormSubmission = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await (0,_services_Email_emailService__WEBPACK_IMPORTED_MODULE_3__.sendEmail)(data);\n            console.log(\"EmailJS response:\", response);\n            // Check the text response instead\n            if (response.status === 200 || response.text === \"OK\") {\n                alert(\"✅ Email sent successfully!\");\n                setData({\n                    First_name: \"\",\n                    Last_name: \"\",\n                    Email: \"\",\n                    Company_name: \"\",\n                    Country: \"\",\n                    Message: \"\"\n                });\n            } else {\n                alert(\"❌ Failed to send email\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error sending email:\", error);\n            alert(\"Something went wrong. Please try again.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen w-screen bg-white flex flex-col relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[50px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, white 0%, #eaf6ff 40%, transparent 70%)\",\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[550px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, #eaf6ff 0%, #b6e1fa 50%, transparent 80%)\",\n                    opacity: 0.6\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[450px] right-0 w-[350px] h-[350px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, #b6e1fa 0%, #eaf6ff 50%, transparent 80%)\",\n                    opacity: 0.5\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-5xl flex flex-col md:flex-row rounded-2xl shadow-2xl bg-white/70 backdrop-blur-md border border-[#eaf6ff] overflow-hidden p-4 md:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:w-1/2 w-full bg-[#b6e1fa] flex flex-col justify-between p-10 rounded-2xl mr-0 md:mr-8 mb-6 md:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-semibold mb-10\",\n                                            children: \"Contact us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-7 h-7 mr-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        d: \"M3 8l7.89 5.26a3 3 0 003.22 0L22 8m-19 8V8a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 117\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-7 h-7 mr-4 mt-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        d: \"M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 122\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"1338 South Oakley ave,\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 65\n                                                        }, this),\n                                                        \"Chicago, IL\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-base\",\n                                            children: \"Follow us:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://linkedin.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"inline-block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.27c-.97 0-1.75-.79-1.75-1.76s.78-1.76 1.75-1.76 1.75.79 1.75 1.76-.78 1.76-1.75 1.76zm15.5 11.27h-3v-5.6c0-1.34-.03-3.07-1.87-3.07-1.87 0-2.16 1.46-2.16 2.97v5.7h-3v-10h2.88v1.36h.04c.4-.75 1.38-1.54 2.85-1.54 3.05 0 3.61 2.01 3.61 4.62v5.56z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 82\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:w-1/2 w-full p-10 bg-white/90 flex flex-col justify-center rounded-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                className: \"space-y-8\",\n                                onSubmit: FormSubmission,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"First Name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                value: data.First_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        First_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Last Name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                value: data.Last_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Last_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Email address\",\n                                        required: true,\n                                        className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                        value: data.Email,\n                                        onChange: (e)=>setData({\n                                                ...data,\n                                                Email: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Company name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                value: data.Company_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Company_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                value: data.Country,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Country: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-gray-400 mb-1\",\n                                                children: \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: \"\",\n                                                rows: 4,\n                                                required: true,\n                                                className: \"w-full border border-gray-200 bg-transparent outline-none py-2 px-3 placeholder-gray-400 rounded-md resize-none focus:border-gray-300\",\n                                                value: data.Message,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Message: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-3 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-start gap-3 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mt-0.5 h-4 w-4 accent-[#a7dfff]\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"I agree to Neartekpod's privacy policy.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-start gap-3 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mt-0.5 h-4 w-4 accent-[#a7dfff]\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"I agree to receive news and articles from Neartekpod\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-8 py-2 border border-black rounded-full font-medium hover:bg-black hover:text-white transition-colors uppercase text-sm\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/contact/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/services/Email/emailService.js":
/*!************************************************!*\
  !*** ./src/app/services/Email/emailService.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendEmail: () => (/* binding */ sendEmail)\n/* harmony export */ });\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emailjs/browser */ \"(ssr)/./node_modules/@emailjs/browser/es/index.js\");\n\nconst sendEmail = async (data)=>{\n    console.log(\"Sending email with data:\", data);\n    const serviceID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;\n    const templateID = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;\n    const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY;\n    const templateParams = data;\n    return _emailjs_browser__WEBPACK_IMPORTED_MODULE_0__[\"default\"].send(serviceID, templateID, templateParams, publicKey);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL3NlcnZpY2VzL0VtYWlsL2VtYWlsU2VydmljZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUF1QztBQUVoQyxNQUFNQyxZQUFZLE9BQU9DO0lBQzVCQyxRQUFRQyxHQUFHLENBQUMsNEJBQTRCRjtJQUMxQyxNQUFNRyxZQUFZQyxRQUFRQyxHQUFHLENBQUNDLDhCQUE4QjtJQUM1RCxNQUFNQyxhQUFhSCxRQUFRQyxHQUFHLENBQUNHLCtCQUErQjtJQUM5RCxNQUFNQyxZQUFZTCxRQUFRQyxHQUFHLENBQUNLLDhCQUE4QjtJQUM1RCxNQUFNQyxpQkFBaUJYO0lBRXZCLE9BQU9GLDZEQUFZLENBQUNLLFdBQVdJLFlBQVlJLGdCQUFnQkY7QUFDN0QsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxXZWIgRmlsZXNcXG5lYXJ0ZWtwb2RcXG5lYXJ0ZWtwb2QgMy4wXFxudHAzLjBcXHNyY1xcYXBwXFxzZXJ2aWNlc1xcRW1haWxcXGVtYWlsU2VydmljZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZW1haWxqcyBmcm9tICdAZW1haWxqcy9icm93c2VyJztcclxuXHJcbmV4cG9ydCBjb25zdCBzZW5kRW1haWwgPSBhc3luYyAoZGF0YSkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coXCJTZW5kaW5nIGVtYWlsIHdpdGggZGF0YTpcIiwgZGF0YSk7XHJcbiAgY29uc3Qgc2VydmljZUlEID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfRU1BSUxKU19TRVJWSUNFX0lEO1xyXG4gIGNvbnN0IHRlbXBsYXRlSUQgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19FTUFJTEpTX1RFTVBMQVRFX0lEO1xyXG4gIGNvbnN0IHB1YmxpY0tleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0VNQUlMSlNfUFVCTElDX0tFWTtcclxuICBjb25zdCB0ZW1wbGF0ZVBhcmFtcyA9IGRhdGE7XHJcblxyXG4gIHJldHVybiBlbWFpbGpzLnNlbmQoc2VydmljZUlELCB0ZW1wbGF0ZUlELCB0ZW1wbGF0ZVBhcmFtcywgcHVibGljS2V5KTtcclxufTtcclxuIl0sIm5hbWVzIjpbImVtYWlsanMiLCJzZW5kRW1haWwiLCJkYXRhIiwiY29uc29sZSIsImxvZyIsInNlcnZpY2VJRCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19FTUFJTEpTX1NFUlZJQ0VfSUQiLCJ0ZW1wbGF0ZUlEIiwiTkVYVF9QVUJMSUNfRU1BSUxKU19URU1QTEFURV9JRCIsInB1YmxpY0tleSIsIk5FWFRfUFVCTElDX0VNQUlMSlNfUFVCTElDX0tFWSIsInRlbXBsYXRlUGFyYW1zIiwic2VuZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/services/Email/emailService.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/@emailjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fcontact%2Fpage&page=%2Fcontact%2Fpage&appPaths=%2Fcontact%2Fpage&pagePath=private-next-app-dir%2Fcontact%2Fpage.tsx&appDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CWeb%20Files%5Cneartekpod%5Cneartekpod%203.0%5Cntp3.0&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();