"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_Email_emailService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../services/Email/emailService */ \"(app-pages-browser)/./src/app/services/Email/emailService.js\");\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/navigation-header */ \"(app-pages-browser)/./src/app/components/navigation-header.tsx\");\n/* harmony import */ var _components_footer_section__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/footer-section */ \"(app-pages-browser)/./src/app/components/footer-section.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ContactPage() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        First_name: \"\",\n        Last_name: \"\",\n        Email: \"\",\n        Company_name: \"\",\n        Country: \"\",\n        Message: \"\"\n    });\n    const FormSubmission = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await (0,_services_Email_emailService__WEBPACK_IMPORTED_MODULE_2__.sendEmail)(data);\n            if (response.status === 200 || response.text === \"OK\") {\n                alert(\"✅ Email sent successfully!\");\n                setData({\n                    First_name: \"\",\n                    Last_name: \"\",\n                    Email: \"\",\n                    Company_name: \"\",\n                    Country: \"\",\n                    Message: \"\"\n                });\n            } else {\n                alert(\"❌ Failed to send email\");\n            }\n        } catch (error) {\n            alert(\"Something went wrong. Please try again.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-white via-[#f6fbff] to-[#eaf6fd]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"flex justify-center items-center px-4 py-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-[1180px] rounded-[25px] border border-[#e0f0ff] bg-white/90 shadow-md overflow-hidden flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[450px] bg-[#a7dfff] p-10 flex flex-col justify-between text-black\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-3xl font-semibold mb-10\",\n                                            children: \"Contact us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"/email-icon.svg\",\n                                                    alt: \"Email\",\n                                                    width: 24,\n                                                    height: 24,\n                                                    className: \"mr-3\",\n                                                    // If you don't have these icons, use the fallback SVG below\n                                                    unoptimized: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-base\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    src: \"/location-icon.svg\",\n                                                    alt: \"Location\",\n                                                    width: 24,\n                                                    height: 24,\n                                                    className: \"mr-3 mt-1\",\n                                                    unoptimized: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-base leading-6\",\n                                                    children: [\n                                                        \"1338 South Oakley ave,\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 72,\n                                                            columnNumber: 77\n                                                        }, this),\n                                                        \"Chicago, IL\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm mr-3\",\n                                            children: \"Follow us:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://linkedin.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                src: \"/linkedin-icon.svg\",\n                                                alt: \"LinkedIn\",\n                                                width: 24,\n                                                height: 24,\n                                                className: \"hover:scale-110 transition-transform\",\n                                                unoptimized: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 p-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: FormSubmission,\n                                className: \"flex flex-col gap-8 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"First Name\",\n                                                    required: true,\n                                                    className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                    value: data.First_name,\n                                                    onChange: (e)=>setData({\n                                                            ...data,\n                                                            First_name: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Last Name\",\n                                                    required: true,\n                                                    className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                    value: data.Last_name,\n                                                    onChange: (e)=>setData({\n                                                            ...data,\n                                                            Last_name: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Email address\",\n                                            required: true,\n                                            className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                            value: data.Email,\n                                            onChange: (e)=>setData({\n                                                    ...data,\n                                                    Email: e.target.value\n                                                })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Company name\",\n                                                    required: true,\n                                                    className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                    value: data.Company_name,\n                                                    onChange: (e)=>setData({\n                                                            ...data,\n                                                            Company_name: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-1/2\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Country\",\n                                                    required: true,\n                                                    className: \"w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300\",\n                                                    value: data.Country,\n                                                    onChange: (e)=>setData({\n                                                            ...data,\n                                                            Country: e.target.value\n                                                        })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-1 text-gray-400\",\n                                                children: \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                rows: 4,\n                                                required: true,\n                                                className: \"w-full border border-gray-200 rounded-md bg-transparent py-2 px-3 outline-none resize-none placeholder-gray-400 focus:border-gray-300\",\n                                                value: data.Message,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Message: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-3 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mt-0.5 h-4 w-4 accent-[#a7dfff] border-gray-300\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"I agree to Neartekpod's privacy policy.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-start gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mt-0.5 h-4 w-4 accent-[#a7dfff] border-gray-300\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"I agree to receive news and articles from Neartekpod\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"px-8 py-2 bg-transparent border border-black rounded-full text-sm font-medium hover:bg-black hover:text-white transition-all uppercase\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer_section__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactPage, \"RhBQJGiAI0gG/sGTvqjdVmsVQzA=\");\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contact/page.tsx\n"));

/***/ })

});