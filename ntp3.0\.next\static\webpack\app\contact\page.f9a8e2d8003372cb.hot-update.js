"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/navigation-header */ \"(app-pages-browser)/./src/app/components/navigation-header.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _services_Email_emailService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/Email/emailService */ \"(app-pages-browser)/./src/app/services/Email/emailService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ContactPage() {\n    _s();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        First_name: \"\",\n        Last_name: \"\",\n        Email: \"\",\n        Company_name: \"\",\n        Country: \"\",\n        Message: \"\"\n    });\n    const FormSubmission = async (e)=>{\n        e.preventDefault();\n        try {\n            const response = await (0,_services_Email_emailService__WEBPACK_IMPORTED_MODULE_3__.sendEmail)(data);\n            console.log(\"EmailJS response:\", response);\n            // Check the text response instead\n            if (response.status === 200 || response.text === \"OK\") {\n                alert(\"✅ Email sent successfully!\");\n                setData({\n                    First_name: \"\",\n                    Last_name: \"\",\n                    Email: \"\",\n                    Company_name: \"\",\n                    Country: \"\",\n                    Message: \"\"\n                });\n            } else {\n                alert(\"❌ Failed to send email\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error sending email:\", error);\n            alert(\"Something went wrong. Please try again.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen w-screen bg-white flex flex-col relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[50px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, white 0%, #eaf6ff 40%, transparent 70%)\",\n                    opacity: 0.7\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[550px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, #eaf6ff 0%, #b6e1fa 50%, transparent 80%)\",\n                    opacity: 0.6\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-[450px] right-0 w-[350px] h-[350px] rounded-full z-40 blur-2xl\",\n                style: {\n                    background: \"radial-gradient(circle, #b6e1fa 0%, #eaf6ff 50%, transparent 80%)\",\n                    opacity: 0.5\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-5xl flex flex-col md:flex-row rounded-2xl shadow-2xl bg-white/70 backdrop-blur-md border border-[#eaf6ff] overflow-hidden p-4 md:p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:w-1/2 w-full bg-[#b6e1fa] flex flex-col justify-between p-10 rounded-2xl mr-0 md:mr-8 mb-6 md:mb-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-semibold mb-10\",\n                                            children: \"Contact us\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-7 h-7 mr-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        d: \"M3 8l7.89 5.26a3 3 0 003.22 0L22 8m-19 8V8a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 117\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 79,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start mb-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-7 h-7 mr-4 mt-1\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        d: \"M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 122\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg\",\n                                                    children: [\n                                                        \"1338 South Oakley ave,\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 65\n                                                        }, this),\n                                                        \"Chicago, IL\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 85,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center mt-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-3 text-base\",\n                                            children: \"Follow us:\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://linkedin.com\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"inline-block\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-6 h-6\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.27c-.97 0-1.75-.79-1.75-1.76s.78-1.76 1.75-1.76 1.75.79 1.75 1.76-.78 1.76-1.75 1.76zm15.5 11.27h-3v-5.6c0-1.34-.03-3.07-1.87-3.07-1.87 0-2.16 1.46-2.16 2.97v5.7h-3v-10h2.88v1.36h.04c.4-.75 1.38-1.54 2.85-1.54 3.05 0 3.61 2.01 3.61 4.62v5.56z\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 82\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:w-1/2 w-full p-10 bg-white/90 flex flex-col justify-center rounded-2xl\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                className: \"space-y-6\",\n                                onSubmit: FormSubmission,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"First Name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-300 bg-transparent outline-none py-2 px-1 placeholder-gray-400\",\n                                                value: data.First_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        First_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Last Name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-300 bg-transparent outline-none py-2 px-1 placeholder-gray-400\",\n                                                value: data.Last_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Last_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        placeholder: \"Email address\",\n                                        required: true,\n                                        className: \"w-full border-b border-gray-300 bg-transparent outline-none py-2 px-1 placeholder-gray-400\",\n                                        value: data.Email,\n                                        onChange: (e)=>setData({\n                                                ...data,\n                                                Email: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Company name\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-300 bg-transparent outline-none py-2 px-1 placeholder-gray-400\",\n                                                value: data.Company_name,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Company_name: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Country\",\n                                                required: true,\n                                                className: \"w-1/2 border-b border-gray-300 bg-transparent outline-none py-2 px-1 placeholder-gray-400\",\n                                                value: data.Country,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Country: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-gray-500 mb-1\",\n                                                htmlFor: \"message\",\n                                                children: \"Message\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                id: \"message\",\n                                                placeholder: \"\",\n                                                rows: 5,\n                                                required: true,\n                                                className: \"w-full border border-gray-200 bg-transparent outline-none py-2 px-2 placeholder-gray-400 rounded-md resize-none\",\n                                                value: data.Message,\n                                                onChange: (e)=>setData({\n                                                        ...data,\n                                                        Message: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col gap-2 mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mr-2 accent-[#b6e1fa]\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"I agree to Neartekpod's privacy policy.\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        className: \"mr-2 accent-[#b6e1fa]\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"I agree to receive news and articles from Neartekpod\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        className: \"mt-4 px-8 py-2 border-2 border-black rounded-full font-semibold hover:bg-black hover:text-white transition-colors\",\n                                        children: \"SEND MESSAGE\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Web Files\\\\neartekpod\\\\neartekpod 3.0\\\\ntp3.0\\\\src\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactPage, \"RhBQJGiAI0gG/sGTvqjdVmsVQzA=\");\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contact/page.tsx\n"));

/***/ })

});