import Image from "next/image"
import Link from "next/link"
import { Mail, MapPin } from "lucide-react"
import NavigationHeader from "../components/navigation-header"
import Footer from "../components/footer-section"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-gray-100">
      <NavigationHeader />
      {/* Hero Section */}
      <section className="relative w-full flex justify-center items-center py-6 px-2 sm:px-4 md:px-8">
        {/* Blue Gradient Background */}
        <div
          className="absolute inset-0 z-0 pointer-events-none"
          style={{
            background: 'radial-gradient(ellipse 60% 50% at 20% 30%, #a7dfff 0%, transparent 100%)',
            filter: 'blur(60px)',
            opacity: 0.7,
          }}
        ></div>

        {/* White Card with Image */}
        <div className="relative z-10 bg-white rounded-[25px] shadow-lg w-full max-w-7xl px-1 sm:px-4 md:px-8 py-4">
          <Image
            src="/About us- hero section.jpg"
            alt="About Us"
            width={1600}
            height={600}
            className="rounded-[25px] w-full h-auto"
            priority
          />
        </div>
      </section>

      {/* Team Stats Section */}
      <section className="px-2 sm:px-4 md:px-8 py-10">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-medium font-montserrat text-gray-800 mb-8 leading-tight">
            We are a team of experienced business, design,<br className="hidden sm:block" />
            technology & process leaders
          </h2>
          <div className="flex flex-col sm:flex-row justify-center gap-4 w-full">
            <div className="flex-1 bg-gradient-to-br from-[#eaf6fd] to-[#fafdff] rounded-[25px] shadow p-4 sm:p-6 w-full">
              <div className="text-3xl sm:text-4xl font-bold text-gray-800 mb-2">150+</div>
              <p className="text-sm sm:text-base font-montserrat text-gray-600">skilled multi disciplinary professionals</p>
            </div>
            <div className="flex-1 bg-gradient-to-br from-[#eaf6fd] to-[#fafdff] rounded-[25px] shadow p-4 sm:p-6 w-full">
              <div className="text-3xl sm:text-4xl font-bold text-gray-800 mb-2">300+</div>
              <p className="text-sm sm:text-base font-montserrat text-gray-600">
                A global network of trusted service<br className="hidden sm:block" />
                providers and technology partners
              </p>
            </div>
            <div className="flex-1 bg-gradient-to-br from-[#eaf6fd] to-[#fafdff] rounded-[25px] shadow p-4 sm:p-6 w-full">
              <div className="text-3xl sm:text-4xl font-bold text-gray-800 mb-2">5</div>
              <p className="text-sm sm:text-base font-montserrat text-gray-600">
                Operational regions around the<br className="hidden sm:block" />
                world
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Founder Section */}
      <section className="px-2 sm:px-4 md:px-8 py-10">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-medium font-montserrat text-gray-800 mb-8">Know our founder</h2>
          <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
            <div className="w-full max-w-xs md:max-w-[288px] h-auto bg-yellow-100 rounded-2xl overflow-hidden flex-shrink-0 mx-auto md:mx-0">
              <Image
                src="/Gopalv3.jpg"
                alt="Gopal Kris Santhana"
                width={288}
                height={320}
                className="w-full h-auto object-cover rounded-2xl"
              />
            </div>
            <div className="flex-1 text-center md:text-left">
              <h3 className="text-xl sm:text-2xl font-medium font-montserrat text-gray-800 mb-2">Gopal "Kris" Santhana</h3>
              <p className="text-gray-600 mb-4 font-medium font-montserrat">Founder, Managing partner</p>
              <p className="text-sm sm:text-base font-medium font-montserrat text-gray-700 leading-relaxed">
                Gopal "Kris" Santhana is the founder and managing partner of a high growth company providing digital
                experiences and technology services. As a trusted technology advisor, he guides and advises CTOs on
                their digital strategy. He has previously held technology leadership roles at Fortune 100 companies
                including Amazon, Discover Financial Services, Walgreens and HSBC. Prior to that he was providing
                technology consultancy for major brands including Allstate, KPMG and JPMorgan Chase. Gopal "Kris"
                Santhana is an alumnus of Oklahoma State University and he has a Bachelor of Engineering from Guindy
                Engineering College, Anna University.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
} 