import NavigationHeader from '../components/navigation-header';
import Image from 'next/image';
import Link from 'next/link';
import FooterSection from '../components/footer-section';

export default function BlogPage() {
  return (
    <div>
      <NavigationHeader />
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-[1180px] mx-auto">
          <div className="relative w-[1180px] h-[382px] border-[1px] rounded-[25px]">
            <div className="absolute inset-0 bg-gradient-to-br from-[rgba(242,245,247,0.24)] via-transparent to-[rgba(167,223,255,0.24)]" />
            <div className="absolute inset-0 border-[1px] border-solid border-transparent">
              <div className="absolute inset-0 border-[1px] border-solid border-transparent" 
                   style={{
                     borderImageSource: 'linear-gradient(253.75deg, rgba(255,255,255,0) 3.46%, rgba(255,255,255,0) 99.94%)',
                     borderImageSlice: 1,
                     borderImageWidth: 1
                   }}
              />
            </div>
            
            {/* Left Image */}
            <div className="absolute left-[31px] top-[26px] w-[563px] h-[329.24px] rounded-[15.8034px]">
              <Image
                src="/blogpage/129770 3.png"
                alt="Blog Content"
                fill
                className="object-cover rounded-[15.8034px]"
              />
            </div>

            {/* Right Content */}
            <div className="absolute left-[643px] top-[34px] w-[461px] h-[297px] gap-[21px]">
              <div className="flex items-center gap-[16px]">
                <span className="font-montserrat font-[400] text-[16px] leading-[100%] letter-spacing-[-0.5px] text-[#000000]">June 19, 2024</span>
                <div className="w-[8px] h-[8px] bg-black rounded-full" />
                <span className="w-[82px] h-[20px] font-montserrat font-[400] text-[16px] leading-[100%] letter-spacing-[-0.5px] text-[#000000]">7 min read</span>
              </div>
              
              <h1 className="w-[461px] h-[34px] font-montserrat font-[700] text-[28px] leading-[100%] letter-spacing-[-0.5px] text-[#000000] mt-[10px]">
                The Future of Hospitality Tech
              </h1>
              
              <p className="w-[461px] h-[140px] font-montserrat font-[500] text-[20px] leading-[28px] letter-spacing-[-0.5px] text-[#000000] mt-[10px]">
                In a fast-evolving industry like hospitality, staying ahead requires continuous reinvention, especially when it comes to offering guests the best experience at an efficient price point. 
              </p>

              <div className="flex items-center justify-between w-[461px] h-[32px]">
                <Link href="/blog/TheFutureofHospitalityTech" className="flex items-center justify-center w-[153px] h-[32px] bg-[#A7DFFF] rounded-[40px] px-[16px] py-[8px]">
                  <span className="font-montserrat font-[600] text-[16px] leading-[100%] letter-spacing-[0px] text-[#00160A]">Read More →</span>
                </Link>
                <div className="flex items-center">
                  <Image
                    src="/blogpage/Ellipse 18.png"
                    alt="Author Avatar"
                    width={32}
                    height={32}
                    className="rounded-full"
                  />
                  <span className="w-[107px] h-[20px] font-montserrat font-[400] text-[16px] leading-[100%] letter-spacing-[-0.5px] text-[#000000] ml-[16px]">By David Sam</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <FooterSection />
    </div>
  );
}

