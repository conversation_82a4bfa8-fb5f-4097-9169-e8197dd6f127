"use client";

import NavigationHeader from "../../components/navigation-header";
import FooterSection from "../../components/footer-section";
import BlueSpot from "../../components/blue-spot";
import Image from "next/image";
import { useRef, useState } from "react";
import { Upload } from "lucide-react";

export default function JobApplicationForm() {
  const [fileName, setFileName] = useState("");
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFileName(e.target.files[0].name);
    }
  };

  const handleRemoveFile = () => {
    setFileName("");
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-[#f6fbff] to-[#eaf6fd] flex flex-col">
      <NavigationHeader />
      <div className="absolute left-0 top-0 w-full h-full pointer-events-none -z-10">
        <BlueSpot xPos={-50} yPos={-50} />
        <BlueSpot xPos={1000} yPos={300} />
      </div>
      <main className="flex-1 flex flex-col items-center px-2 sm:px-0">
        <div className="w-full max-w-5xl mt-8 mb-8">


            
          <div className="text-sm text-[#1c274c] mb-4 flex items-center gap-2 font-montserrat">
            <span className="opacity-70">Career</span>
            <span className="mx-1">&#62;</span>
            <span className="font-semibold">Job application</span>
          </div>



          <div className="rounded-2xl overflow-hidden mb-8 relative">
            <Image
              src="/job-application.jpg"
              alt="Job Application Hero"
              width={1200}
              height={350}
              className="w-full h-[260px] sm:h-[350px] object-cover object-top"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-[#1c274c]/80 to-transparent flex flex-col justify-end p-8">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-2">Ready to Join our team?</h2>
              <p className="text-white text-base sm:text-lg font-montserrat">Take the First Step Towards an Exciting Career with Us!</p>
            </div>
          </div>



          <form className="bg-white rounded-2xl shadow-lg p-6 sm:p-10 mb-8 font-montserrat">
            <p className="mb-6 text-[#1c274c] text-base sm:text-lg">Fill out the form below and click on submit to apply for the job</p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-4">
              <div>
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Full name <span className="text-red-500">*</span></label>
                <input type="text" required placeholder="Enter your full name" className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#a7dfff] text-black" />
              </div>
              <div>
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Email ID <span className="text-red-500">*</span></label>
                <input type="email" required placeholder="Enter your email ID" className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#a7dfff] text-black" />
              </div>
              <div>
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Phone number <span className="text-red-500">*</span></label>
                <input type="tel" required placeholder="+91 98765 43210" className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#a7dfff] text-black" 
                  onKeyPress={(e) => {
                    const allowed = /[0-9+]/;
                    if (!allowed.test(e.key)) {
                      e.preventDefault();
                    }
                  }}
                />
                 <div className="text-xs mt-0.5 text-gray-500 ml-1">Enter the contact number with the country code</div>
              </div>
              <div>
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Applying for <span className="text-red-500">*</span></label>
                <select required className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#a7dfff] text-black">
                  <option value="">Select a role</option>
                  <option>OIC lead developer</option>
                  <option>Senior Software Engineer</option>
                  <option>Senior Software Developer</option>
                  <option>Software Engineer</option>
                  <option>Software Developer</option>
                </select>
              </div>
              <div className="sm:col-span-2">
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Upload your documents <span className="text-red-500">*</span></label>
                <div className="mb-2 flex flex-col items-start gap-2 w-full">
                  {fileName && (
                    <div className="flex items-center bg-gray-100 rounded-lg px-4 h-12 shadow min-w-0 max-w-[260px] transition-all duration-200">
                      <img src="/blogpage/Frame 463.png" alt="PDF Icon" className="w-6 h-6 mr-3" />
                      <span className="text-base text-gray-800 truncate flex-1 pl-1" style={{ maxWidth: '120px' }}>{fileName}</span>
                      <button type="button" className="text-gray-400 hover:text-red-500 p-1 ml-0" onClick={handleRemoveFile} aria-label="Remove file">&times;</button>
                    </div>
                  )}
                  <input
                    type="file"
                    accept=".pdf,.doc,.docx"
                    className="hidden"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                  />
                  <button
                    type="button"
                    className="border border-[#00a2ff] text-[#00a2ff] rounded-full px-3 h-8 flex items-center gap-2 text-base font-medium hover:bg-[#a7dfff]/30 transition-all duration-200 border-[1.5px] mt-2"
                    style={{ minWidth: '130px' }}
                    onClick={() => fileInputRef.current && fileInputRef.current.click()}
                  >
                    <Upload className="w-5 h-5 mr-2" /> Add File
                  </button>
                </div>
                <div className="text-xs text-gray-500 ml-1">File size must be less than 2mb. File formats supported: PDF, DOC, DOCX</div>
              </div>
              <div className="sm:col-span-2">
                <label className="block mb-2 text-sm font-medium text-[#1c274c]">Message</label>
                <textarea rows={3} placeholder="" className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-[#a7dfff] resize-none text-black" />
              </div>
            </div>
            <button type="submit" className="mt-4 px-8 py-2 bg-[#eaf6fd] text-[#1c274c] border border-[#1c274c] rounded-full font-semibold hover:bg-[#1c274c] hover:text-white transition text-base">Submit</button>
          </form>
        </div>
      </main>
      <FooterSection />
    </div>
  );
}
