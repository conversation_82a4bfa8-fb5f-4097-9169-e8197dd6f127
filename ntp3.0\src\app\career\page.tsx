"use client";
import NavigationHeader from "../components/navigation-header";
import JobDescription from "../components/job-description";
import BlueSpot from "../components/blue-spot";
import FooterSection from "../components/footer-section";
import { useState } from "react";

export default function Career() {
  const seniorSoftwareEngineer = {
    title: "Senior Software Engineer",
    experience: "Exp- 4+ years",
    location: "Trichy",
    employmentType: "Full time",
    responsibilities: [
      "Collaborate with a dynamic team of engineers, including mentoring more junior engineers in the workgroup",
      "Learn and share ideas continuously",
      "Drive for quality in everything you do and continuously improve the quality of the systems on which you work",
      "Collaborate across team boundaries to define and drive team charter and vision",
      "Provide technical input across the Windows Core and Azure teams",
      "Design and implement new virtualization related features for Hyper-V and Windows",
    ],
    qualifications: [
      "Bachelor's Degree in Computer Science, or related technical discipline AND 4+ years technical engineering experience with coding in languages including, but not limited to, Rust, C, or C++",
      "OR equivalent experience",
    ],
  };

  const seniorSoftwareDeveloper = {
    title: "Senior Software Developer",
    experience: "Exp- 4+ years",
    location: "Trichy",
    employmentType: "Full time",
    responsibilities: [
      "Collaborate with a dynamic team of engineers, including mentoring more junior engineers in the workgroup",
      "Learn and share ideas continuously",
      "Drive for quality in everything you do and continuously improve the quality of the systems on which you work",
      "Collaborate across team boundaries to define and drive team charter and vision",
      "Provide technical input across the Windows Core and Azure teams",
      "Design and implement new virtualization related features for Hyper-V and Windows",
    ],
    qualifications: [
      "Bachelor's Degree in Computer Science, or related technical discipline AND 4+ years technical engineering experience with coding in languages including, but not limited to, Rust, C, or C++",
      "OR equivalent experience",
    ],
  };
const SoftwareEngineer = {
    title: "Software Engineer",
    experience: "Exp- 1+ years",
    location: "Trichy",
    employmentType: "Full time",
    responsibilities: [
      "Collaborate with a dynamic team of engineers, including mentoring more junior engineers in the workgroup",
      "Learn and share ideas continuously",
      "Drive for quality in everything you do and continuously improve the quality of the systems on which you work",
      "Collaborate across team boundaries to define and drive team charter and vision",
      "Provide technical input across the Windows Core and Azure teams",
      "Design and implement new virtualization related features for Hyper-V and Windows",
    ],
    qualifications: [
      "Bachelor's Degree in Computer Science, or related technical discipline AND 4+ years technical engineering experience with coding in languages including, but not limited to, Rust, C, or C++",
      "OR equivalent experience",
    ],
  };

  const SoftwareDeveloper = {
    title: "Software Developer",
    experience: "Exp- 1+ years",
    location: "Trichy",
    employmentType: "Full time",
    responsibilities: [
      "Collaborate with a dynamic team of engineers, including mentoring more junior engineers in the workgroup",
      "Learn and share ideas continuously",
      "Drive for quality in everything you do and continuously improve the quality of the systems on which you work",
      "Collaborate across team boundaries to define and drive team charter and vision",
      "Provide technical input across the Windows Core and Azure teams",
      "Design and implement new virtualization related features for Hyper-V and Windows",
    ],
    qualifications: [
      "Bachelor's Degree in Computer Science, or related technical discipline AND 4+ years technical engineering experience with coding in languages including, but not limited to, Rust, C, or C++",
      "OR equivalent experience",
    ],
  };

  const jobs = [
    seniorSoftwareEngineer,
    seniorSoftwareDeveloper,
    SoftwareEngineer,
    SoftwareDeveloper,
    seniorSoftwareEngineer,
    seniorSoftwareDeveloper,
    SoftwareEngineer,
    SoftwareDeveloper,
    seniorSoftwareEngineer,
    seniorSoftwareDeveloper,
    SoftwareEngineer,
    SoftwareDeveloper,
  ];
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  return (
    <div className="relative overflow-hidden">
      <NavigationHeader />
      <h1 className="text-4xl w-100px text-left mt-15 ml-20 text-[#1c274c]">
        Job Opportunities
      </h1>
      {jobs.map((job, idx) => (
        <JobDescription
          key={idx}
          jobData={job}
          isExpanded={expandedIndex === idx}
          onToggle={() => setExpandedIndex(expandedIndex === idx ? null : idx)}
        />
      ))}
      <p className="text-center mt-4 mb-2 text-lg text-gray-500">
        Explore exciting career opportunities with us! We are always looking for talented individuals to join our team.
      </p>
      <BlueSpot xPos={250} yPos={150} />
      <BlueSpot xPos={1400} yPos={600} />
      <BlueSpot xPos={-80} yPos={1200} />
      <FooterSection />
    </div>
  );
}