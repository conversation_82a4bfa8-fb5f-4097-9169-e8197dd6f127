"use client";

import React from "react";

export default function EmailForm() {
    return (
        <form
            className="absolute left-1/2 top-1/2 flex flex-col xs:flex-row items-center"
            style={{
                width: '350px',
                height: 40,
                gap: 12,
                transform: "translate(-50%, -50%)",
                maxWidth: '100%',
            }}
            onSubmit={e => e.preventDefault()}
        >
            <input
                type="email"
                placeholder="Enter your email ID"
                style={{
                    width: '100%',
                    minWidth: 0,
                    height: 40,
                    padding: "10px 12px",
                    borderRadius: 16,
                    borderWidth: 1,
                    background: "#CAECFF",
                    color: "#A6A6A6",
                    fontFamily: "Montserrat, sans-serif",
                    fontWeight: 500,
                    fontSize: 16,
                    lineHeight: "100%",
                    letterSpacing: 0,
                    border: "1px solid #A6A6A6",
                    boxSizing: 'border-box',
                }}
            />
            <button
                type="submit"
                style={{
                    width: 107,
                    height: 40,
                    padding: "10px 12px",
                    borderRadius: 16,
                    background: "#F0F0F5",
                    color: "#1A1A13",
                    fontFamily: "Montserrat, sans-serif",
                    fontWeight: 500,
                    fontSize: 16,
                    lineHeight: "100%",
                    letterSpacing: 0,
                    border: "none",
                    cursor: "pointer"
                }}
            >
                Mail Us
            </button>
            <style jsx>{`
                @media (min-width: 640px) {
                    form {
                        width: 441px !important;
                        flex-direction: row !important;
                        gap: 14px !important;
                    }
                }
            `}</style>
        </form>
    );
} 