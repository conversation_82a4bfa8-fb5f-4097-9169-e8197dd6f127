"use client"

import React from 'react';

interface BlueSpotProps {
  xPos: number;
  yPos: number;
}

const BlueSpot: React.FC<BlueSpotProps> = ({ xPos, yPos }) => {
  return (
    <div
      className="absolute rounded-full"
      style={{
        width: '202px',
        height: '194px',
        backgroundColor: '#00A2FF',
        filter: 'blur(100px)',
        left: `${xPos}px`,
        top: `${yPos}px`,
        zIndex: -1,
      }}
    ></div>
  );
};

export default BlueSpot; 