import { Mail, Phone, MapPin, Twitter, Facebook, Instagram } from "lucide-react"
import Image from "next/image"


export default function FooterSection() {
  return (
    <footer className="bg-[#e3e7eb] px-6 pt-12 pb-6 ">
      <div className="max-w-7xl mx-auto ">
        {/* Main footer content */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5 mb-3">
          {/* Reach US Section */}
          <div>
            <h3 className="text-black text-[24px] font-medium leading-[100%] tracking-[0px] text-center capitalize font-Montserrat mb-6">
              Reach US
            </h3>
            <div className="space-y-4 flex flex-col items-center">
              <div className="flex justify-end gap-3">
                <Mail className="w-5 h-5 text-[#424b5a]" />
                <span className="text-[#424b5a] font-Montserrat"><EMAIL></span>
              </div>
              <div className="flex items-center gap-3">
                <Phone className="w-5 h-5 text-[#424b5a]" />
                <span className="text-[#424b5a]">+ (847) 450 - 3728</span>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="w-5 h-5 text-[#424b5a] mt-0.5" />
                <div className="text-[#424b5a]">
                  <div>575 W Helen Rd</div>
                  <div>Palatine, IL 60067</div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links Section */}
          <div className="font-Montserrat flex flex-col items-center justify-center">

            <h3 className="text-[#000000] text-xl mb-6 text-center">Quick Links</h3>
            <div className="space-y-3 item-center">
              <button
                className="w-[180px] h-[40px] flex items-center justify-center rounded-full border border-[#c2d1d9] text-[#424b5a] hover:bg-[#c2d1d9] cursor-pointer"
                style={{
                  gap: '10px',
                  padding: '8px 16px',
                  borderRadius: '40px',
                  borderWidth: '1px',
                }}
              >
                View Our Services
              </button>
              <button
                className="w-[180px] h-[40px] flex items-center justify-center rounded-full border border-[#c2d1d9] text-[#424b5a] hover:bg-[#c2d1d9] cursor-pointer font-Montserrat"
                style={{
                  gap: '10px',
                  padding: '8px 16px',
                  borderRadius: '40px',
                  borderWidth: '1px',
                }}
              >
                Our Offices
              </button>
              <button
                className="w-[180px] h-[40px] flex items-center justify-center rounded-full border border-[#c2d1d9] text-[#424b5a] hover:bg-[#c2d1d9] cursor-pointer"
                style={{
                  gap: '10px',
                  padding: '8px 16px',
                  borderRadius: '40px',
                  borderWidth: '1px',
                }}
              >
                About US
              </button>
            </div>
          </div>

          {/* Get in touch Section */}
          <div className="flex flex-col justify-center">
            <h3 className="text-[#000000] text-xl mb-6 leading-tight font-Montserrat text-[28px]">
              Get in touch with us. Let us help you with any of your tech needs.
            </h3>
            <div className="flex items-center justify-between mb-6">
              <Image
                src="homepage/ntp-logo2.svg"
                alt="NearTekPod Logo"
                width={138}
                height={40}
                priority
              />
              <button className="bg-[#00a2ff] hover:bg-[#0088cc] text-white rounded-full px-6 h-9 font-Montserrat cursor-pointer">CONTACT US</button>
            </div>
          </div>
        </div>

        {/* Bottom section with social icons and copyright */}
        <div className="flex flex-col md:flex-row justify-between items-center pt-3 border-t border-[#c2d1d9]">
          <div className="flex gap-4 mb-4 md:mb-0">
            <a href="https://x.com/YOUR_HANDLE" target="_blank" rel="noopener noreferrer">
              <Twitter className="w-5 h-5 text-[#505d68] hover:text-[#00a2ff] cursor-pointer" />
            </a>
            <a href="https://facebook.com/YOUR_PAGE" target="_blank" rel="noopener noreferrer">
              <Facebook className="w-5 h-5 text-[#505d68] hover:text-[#00a2ff] cursor-pointer" />
            </a>
            <a href="https://instagram.com/YOUR_HANDLE" target="_blank" rel="noopener noreferrer">
              <Instagram className="w-5 h-5 text-[#505d68] hover:text-[#00a2ff] cursor-pointer" />
            </a>
          </div>
          <div className="text-[#505d68] text-sm">©2025 - NearTekPod | All right reserved</div>
        </div>
      </div>
    </footer>
  )
}
