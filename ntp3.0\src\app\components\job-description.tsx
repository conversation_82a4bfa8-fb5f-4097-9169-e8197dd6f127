"use client"

import { useState } from 'react';
import Link from "next/link";

type JobData = {
  title: string;
  experience: string;
  location: string;
  employmentType: string;
  responsibilities: string[];
  qualifications: string[];
};

export default function JobDescription({
  jobData,
  isExpanded,
  onToggle,
}: {
  jobData: JobData;
  isExpanded: boolean;
  onToggle: () => void;
}) {
  return (
    <div className="container mx-auto px-3 sm:px-4 md:px-8 w-[90%] max-w-6xl mt-4 sm:mt-6 mb-4 sm:mb-6 p-3 sm:p-4 rounded-2xl" style={{ background: 'linear-gradient(to right, #dbf2ff, white)' }}>
      <div className="flex justify-between items-center">
        <div className="flex-grow">
          <h1 className="text-2xl sm:text-3xl text-left text-[#1c274c] mb-1">{jobData.title}</h1>
          <p className="text-sm sm:text-base text-black mb-3 sm:mb-4">{jobData.experience}, {jobData.location}, {jobData.employmentType}</p>
        </div>
        <div className="flex-shrink-0 ml-4">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="black" className="w-5 h-5 sm:w-6 sm:h-6 transform transition-transform duration-300 cursor-pointer" onClick={onToggle}>
            {isExpanded ? (
              <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 10.5 12 3m0 0 7.5 7.5M12 3v18" />
            ) : (
              <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 13.5 12 21m0 0-7.5-7.5M12 21V3" />
            )}
          </svg>
        </div>
      </div>

      <div className={`grid transition-all duration-500 ease-in-out ${isExpanded ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}>
        <div className="overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mt-3">
            <div>
              <h2 className="text-base sm:text-lg text-[#1c274c] mb-2">Responsibilities</h2>
              <ul className="list-disc text-black list-inside mb-3 space-y-1 text-xs sm:text-sm">
                {jobData.responsibilities.map((responsibility, index) => (
                  <li key={index} className="mb-1">{responsibility}</li>
                ))}
              </ul>
            </div>
            <div>
              <h2 className="text-base sm:text-lg text-[#1c274c] mb-2">Qualifications</h2>
              <ul className="list-disc text-black list-inside mb-3 space-y-1 text-xs sm:text-sm">
                {jobData.qualifications.map((qualification, index) => (
                  <li key={index} className="mb-1">{qualification}</li>
                ))}
              </ul>
            </div>
          </div>
          <p className="text-xs sm:text-sm text-black mt-3 sm:mt-4">
            If you are passionate about technology and eager to learn, we would love to hear from you!
          </p>
          <div className="flex justify-center mt-6 sm:mt-8">
            <Link href="/career/job-application-form" className="w-full sm:w-auto">
              <button className="w-full sm:w-auto px-6 sm:px-8 py-2 sm:py-2.5 border-2 border-[#1c274c] text-[#1c274c] font-semibold rounded-full hover:bg-[#1c274c] hover:text-white transition duration-300 text-sm sm:text-base">
                APPLY NOW
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}