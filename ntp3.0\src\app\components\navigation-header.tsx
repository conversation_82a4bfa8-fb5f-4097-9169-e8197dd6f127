"use client"

import { Home, Users, User, Briefcase, FileText, Menu, X } from "lucide-react"
import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"

export default function Component() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const pathname = usePathname();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="bg-[#ffffff] w-full">
      <nav
        className="container flex items-center justify-between px-4 md:px-8 max-w-7xl mx-auto"
        style={{ height: '72px' }}
      >
        {/* Logo */}
        <Link href="/">
          <div className="flex items-center space-x-2">
            <Image
              src="/ntp-logo.png"
              alt="NearTekPod Logo"
              width={219}
              height={63}
              priority
            />
          </div>
        </Link>

        {/* Desktop Navigation Menu - Hidden on mobile */}
        <div className="hidden md:flex items-center space-x-2">
          {/* Home - Active */}
          <Link href="/">
            <button
              className={`flex items-center space-x-2 px-4 text-[#1c274c] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === "/" ? " bg-[#a7dfff]" : " bg-[#e7eaee]")}
              style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif', cursor: 'pointer' }}
            >
              <Home className="w-4 h-4" />
              <span>Home</span>
            </button>
          </Link>

          {/* Services */}
          <Link href="/services">
            <button
              className={`flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === "/services" ? " bg-[#a7dfff]" : " bg-[#e7eaee]")}
              style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif',cursor: 'pointer' }}
            >
              <Users className="w-4 h-4" />
              <span>Services</span>
            </button>
          </Link>

          {/* About Us */}
          <Link href="/about">
            <button
              className={`flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === "/about" ? " bg-[#a7dfff]" : " bg-[#e7eaee]")}
              style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif',cursor: 'pointer' }}
            >
              <User className="w-4 h-4" />
              <span>About Us</span>
            </button>
          </Link>

          {/* Career */}
          <Link href="/career">
            <button
              className={`flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === "/career" ? " bg-[#a7dfff]" : " bg-[#e7eaee]")}
              style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif',cursor: 'pointer' }}
            >
              <Briefcase className="w-4 h-4" />
              <span>Career</span>
            </button>
          </Link>

          {/* Blog */}
          <Link href="/blog">
            <button
              className={`flex items-center space-x-2 px-4 text-[#505d68] rounded-full font-medium hover:bg-[#a7dfff] transition-colors` + (pathname === "/blog" ? " bg-[#a7dfff]" : " bg-[#e7eaee]")}
              style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif',cursor: 'pointer' }}
            >
              <FileText className="w-4 h-4" />
              <span>Blog</span>
            </button>
          </Link>

          {/* Contact Us */}
          <Link href="/contact">
          <button
            className={`px-6 border-1 border-[#00a2ff] text-[#00a2ff] rounded-full font-semibold hover:bg-[#a7dfff] hover:text-[#1c274c] transition-colors` + (pathname === "/contact" ? " bg-[#a7dfff]" : " bg-transparent")}
            style={{ height: '40px', fontSize: '12px', fontFamily: 'Montserrat, sans-serif',cursor: 'pointer' }}
          >
            CONTACT US
          </button>
          </Link>
        </div>

        {/* Mobile Hamburger Button */}
        <button className="md:hidden p-2 text-[#1c274c]" onClick={toggleMobileMenu} aria-label="Toggle mobile menu">
          {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
        </button>
      </nav>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40" onClick={toggleMobileMenu} />
      )}

      {/* Mobile Menu */}
      <div
        className={`md:hidden fixed top-0 right-0 h-full w-80 bg-[#ffffff] shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${isMobileMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
      >
        <div className="p-6">
          {/* Mobile Menu Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-2">
              <Image
                src="/ntp-logo.png"
                alt="NearTekPod Logo"
                width={138}
                height={40}
                priority
              />
            </div>
            <button onClick={toggleMobileMenu} className="p-2 text-[#1c274c]" aria-label="Close mobile menu">
              <X className="w-6 h-6" />
            </button>
          </div>




          {/* Mobile Navigation Items */}
          <div className="space-y-4">
            {/* Home - Active */}
            <Link href="/">
              <button className="w-full flex items-center space-x-3 px-4 py-3 bg-[#a7dfff] text-[#1c274c] rounded-lg font-medium">
                <Home className="w-5 h-5 cursor-pointer" />
                <span>Home</span>
              </button>
            </Link>

            {/* Services */}
            <Link href="/services">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors">
                <Users className="w-5 h-5 cursor-pointer" />
                <span>Services</span>
              </button>
            </Link>

            {/* About Us */}
            <Link href="/about">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors">
                <User className="w-5 h-5 cursor-pointer" />
                <span>About Us</span>
              </button>
            </Link>

            {/* Career */}
            <Link href="/career">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors">
                <Briefcase className="w-5 h-5 cursor-pointer" />
                <span>Career</span>
              </button>
            </Link>

            {/* Blog */}
            <Link href="/blog">
              <button className="w-full flex items-center space-x-3 px-4 py-3 text-[#505d68] rounded-lg font-medium hover:bg-[#e7eaee] transition-colors">
                <FileText className="w-5 h-5 cursor-pointer" />
                <span>Blog</span>
              </button>
            </Link>

            {/* Mobile Contact Us Button */}
            <button className="w-full mt-6 px-6 py-3 border-2 border-[#00a2ff] text-[#00a2ff] rounded-lg font-semibold hover:bg-[#00a2ff] hover:text-[#ffffff] transition-colors cursor-pointer">
              CONTACT US
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
