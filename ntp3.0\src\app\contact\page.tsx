"use client";

import NavigationHeader from "../components/navigation-header";
import React,{ useState } from "react";
import {sendEmail} from "../services/Email/emailService";

export default function ContactPage() {
  const [data, setData] = useState({
    First_name: "",
    Last_name: "",
    Email: "",
    Company_name: "",
    Country: "",
    Message: "",
  })
  const FormSubmission = async (e: React.FormEvent) => {
    e.preventDefault();
   try {
  const response = await sendEmail(data);
  console.log("EmailJS response:", response);

  // Check the text response instead
  if (response.status === 200 || response.text === "OK") {
    alert("✅ Email sent successfully!");
    setData({
      First_name: "",
      Last_name: "",
      Email: "",
      Company_name: "",
      Country: "",
      Message: "",
    });
  } else {
    alert("❌ Failed to send email");
  }
} catch (error) {
  console.error("❌ Error sending email:", error);
  alert("Something went wrong. Please try again.");
}

    }
  return (
    <div className="h-screen w-screen bg-white flex flex-col relative overflow-hidden">
      {/* 🔹 Navigation */}
      <NavigationHeader />

      {/* 🔹 Radial Light Blobs (background) */}
      <div
        className="absolute top-[50px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl"
        style={{
          background: "radial-gradient(circle, white 0%, #eaf6ff 40%, transparent 70%)",
          opacity: 0.7
        }}
      ></div>
      <div
        className="absolute top-[550px] left-[30px] w-[400px] h-[400px] rounded-full z-40 blur-2xl"
        style={{
          background: "radial-gradient(circle, #eaf6ff 0%, #b6e1fa 50%, transparent 80%)",
          opacity: 0.6
        }}
      ></div>
      <div
        className="absolute top-[450px] right-0 w-[350px] h-[350px] rounded-full z-40 blur-2xl"
        style={{
          background: "radial-gradient(circle, #b6e1fa 0%, #eaf6ff 50%, transparent 80%)",
          opacity: 0.5
        }}
      ></div>

      {/* 🔹 Main Contact Box */}
      <div className="flex flex-1 items-center justify-center">
        <div className="w-full max-w-5xl flex flex-col md:flex-row rounded-2xl shadow-2xl bg-white/70 backdrop-blur-md border border-[#eaf6ff] overflow-hidden p-4 md:p-6">
          {/* Left Info Box */}
          <div className="md:w-1/2 w-full bg-[#b6e1fa] flex flex-col justify-between p-10 rounded-2xl mr-0 md:mr-8 mb-6 md:mb-0">
            <div>
              <h2 className="text-4xl font-semibold mb-10">Contact us</h2>
              <div className="flex items-center mb-8">
                {/* Email Icon */}
                <svg className="w-7 h-7 mr-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M3 8l7.89 5.26a3 3 0 003.22 0L22 8m-19 8V8a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" /></svg>
                <span className="text-lg"><EMAIL></span>
              </div>
              <div className="flex items-start mb-12">
                {/* Location Icon */}
                <svg className="w-7 h-7 mr-4 mt-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z" /></svg>
                <span className="text-lg">1338 South Oakley ave,<br />Chicago, IL</span>
              </div>
            </div>
            <div className="flex items-center mt-8">
              <span className="mr-3 text-base">Follow us:</span>
              {/* LinkedIn Icon */}
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="inline-block">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.27c-.97 0-1.75-.79-1.75-1.76s.78-1.76 1.75-1.76 1.75.79 1.75 1.76-.78 1.76-1.75 1.76zm15.5 11.27h-3v-5.6c0-1.34-.03-3.07-1.87-3.07-1.87 0-2.16 1.46-2.16 2.97v5.7h-3v-10h2.88v1.36h.04c.4-.75 1.38-1.54 2.85-1.54 3.05 0 3.61 2.01 3.61 4.62v5.56z" /></svg>
              </a>
            </div>
          </div>
          {/* Right Form Box */}
          <div className="md:w-1/2 w-full p-10 bg-white/90 flex flex-col justify-center rounded-2xl">
            <form className="space-y-8" onSubmit={FormSubmission}>
              <div className="flex gap-6">
                <input 
                  type="text" 
                  placeholder="First Name" 
                  required 
                  className="w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300" 
                  value={data.First_name} 
                  onChange={(e) => setData({ ...data, First_name: e.target.value })} 
                />
                <input 
                  type="text" 
                  placeholder="Last Name" 
                  required 
                  className="w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300" 
                  value={data.Last_name} 
                  onChange={(e) => setData({ ...data, Last_name: e.target.value })} 
                />
              </div>
              
              <input 
                type="email" 
                placeholder="Email address" 
                required 
                className="w-full border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300" 
                value={data.Email} 
                onChange={(e) => setData({ ...data, Email: e.target.value })} 
              />
              
              <div className="flex gap-6">
                <input 
                  type="text" 
                  placeholder="Company name" 
                  required 
                  className="w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300" 
                  value={data.Company_name} 
                  onChange={(e) => setData({ ...data, Company_name: e.target.value })} 
                />
                <input 
                  type="text" 
                  placeholder="Country" 
                  required 
                  className="w-1/2 border-b border-gray-200 bg-transparent outline-none py-2 px-0 placeholder-gray-400 focus:border-gray-300" 
                  value={data.Country} 
                  onChange={(e) => setData({ ...data, Country: e.target.value })} 
                />
              </div>
              
              <div>
                <div className="text-gray-400 mb-1">Message</div>
                <textarea 
                  placeholder="" 
                  rows={4} 
                  required 
                  className="w-full border border-gray-200 bg-transparent outline-none py-2 px-3 placeholder-gray-400 rounded-md resize-none focus:border-gray-300" 
                  value={data.Message} 
                  onChange={(e) => setData({ ...data, Message: e.target.value })}
                />
              </div>
              
              <div className="flex flex-col gap-3 mt-2">
                <label className="flex items-start gap-3 text-sm text-gray-500">
                  <input 
                    type="checkbox" 
                    className="mt-0.5 h-4 w-4 accent-[#a7dfff]" 
                    required 
                  />
                  I agree to Neartekpod's privacy policy.
                </label>
                <label className="flex items-start gap-3 text-sm text-gray-500">
                  <input 
                    type="checkbox" 
                    className="mt-0.5 h-4 w-4 accent-[#a7dfff]" 
                    required 
                  />
                  I agree to receive news and articles from Neartekpod
                </label>
              </div>
              
              <div className="mt-6">
                <button 
                  type="submit" 
                  className="px-8 py-2 border border-black rounded-full font-medium hover:bg-black hover:text-white transition-colors uppercase text-sm"
                >
                  Send Message
                </button>
              </div>
            </form>
            
          </div>   
        </div>
      </div>
    </div>
  );
}
