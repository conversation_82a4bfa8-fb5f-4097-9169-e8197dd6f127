"use client";

import React, { useState } from "react";
import { sendEmail } from "../services/Email/emailService";
import NavigationHeader from "../components/navigation-header";
import FooterSection from "../components/footer-section";

export default function ContactPage() {
  const [data, setData] = useState({
    First_name: "",
    Last_name: "",
    Email: "",
    Company_name: "",
    Country: "",
    Message: "",
  });

  const FormSubmission = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await sendEmail(data);
      if (response.status === 200 || response.text === "OK") {
        alert("✅ Email sent successfully!");
        setData({
          First_name: "",
          Last_name: "",
          Email: "",
          Company_name: "",
          Country: "",
          Message: "",
        });
      } else {
        alert("❌ Failed to send email");
      }
    } catch (error) {
      alert("Something went wrong. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-[#f2f9fe] to-[#dff0fc]">
      {/* 🔹 Navigation Header */}
     <NavigationHeader />

      {/* 🔹 Main Contact Section */}
      <section className="flex justify-center items-center px-4 py-10">
        <div className="relative w-[1180px] h-[700px] rounded-[25px] border border-[#8ecfff] bg-white/80 backdrop-blur-md flex shadow-md overflow-hidden">
          {/* Left Panel */}
          <div className="absolute left-0 top-0 w-[572px] h-full bg-[#A7DFFF] p-10 flex flex-col justify-between rounded-l-[25px] text-black">
            <div>
              <h2 className="text-3xl font-semibold mb-10">Contact us</h2>
              <div className="flex items-center mb-8">
                <svg className="w-6 h-6 mr-3" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M3 8l7.89 5.26a3 3 0 003.22 0L22 8m-19 8V8a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                </svg>
                <span className="text-base"><EMAIL></span>
              </div>
              <div className="flex items-start mb-12">
                <svg className="w-6 h-6 mr-3 mt-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z" />
                </svg>
                <span className="text-base leading-6">1338 South Oakley ave,<br />Chicago, IL</span>
              </div>
            </div>
            <div className="flex items-center">
              <span className="text-sm mr-3">Follow us:</span>
              <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer">
                <svg className="w-6 h-6 hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19 0h-14c-2.76 0-5 2.24-5 5v14c0 2.76 2.24 5 5 5h14c2.76 0 5-2.24 5-5v-14c0-2.76-2.24-5-5-5zm-11 19h-3v-10h3v10zm-1.5-11.27c-.97 0-1.75-.79-1.75-1.76s.78-1.76 1.75-1.76 1.75.79 1.75 1.76-.78 1.76-1.75 1.76zm15.5 11.27h-3v-5.6c0-1.34-.03-3.07-1.87-3.07-1.87 0-2.16 1.46-2.16 2.97v5.7h-3v-10h2.88v1.36h.04c.4-.75 1.38-1.54 2.85-1.54 3.05 0 3.61 2.01 3.61 4.62v5.56z" />
                </svg>
              </a>
            </div>
          </div>

          {/* Right Form Panel */}
          <div className="ml-[572px] w-full pt-[72px] pr-[40px] pb-[32px] pl-[32px]">
            <form onSubmit={FormSubmission} className="flex flex-col gap-[32px] text-sm text-gray-600">
              <div className="flex gap-6">
                <input type="text" placeholder="First Name" required className="w-1/2 border-b border-gray-300 bg-transparent outline-none py-1.5 placeholder-gray-500" value={data.First_name} onChange={(e) => setData({ ...data, First_name: e.target.value })} />
                <input type="text" placeholder="Last Name" required className="w-1/2 border-b border-gray-300 bg-transparent outline-none py-1.5 placeholder-gray-500" value={data.Last_name} onChange={(e) => setData({ ...data, Last_name: e.target.value })} />
              </div>
              <input type="email" placeholder="Email address" required className="w-full border-b border-gray-300 bg-transparent outline-none py-1.5 placeholder-gray-500" value={data.Email} onChange={(e) => setData({ ...data, Email: e.target.value })} />
              <div className="flex gap-6">
                <input type="text" placeholder="Company name" required className="w-1/2 border-b border-gray-300 bg-transparent outline-none py-1.5 placeholder-gray-500" value={data.Company_name} onChange={(e) => setData({ ...data, Company_name: e.target.value })} />
                <input type="text" placeholder="Country" required className="w-1/2 border-b border-gray-300 bg-transparent outline-none py-1.5 placeholder-gray-500" value={data.Country} onChange={(e) => setData({ ...data, Country: e.target.value })} />
              </div>
              <div>
                <label className="text-gray-500 mb-1 block">Message</label>
                <textarea rows={5} placeholder="" required className="w-full border border-gray-300 rounded-md bg-transparent py-2 px-3 outline-none resize-none placeholder-gray-500" value={data.Message} onChange={(e) => setData({ ...data, Message: e.target.value })} />
              </div>
              <div className="flex flex-col gap-2">
                <label className="flex items-start gap-2">
                  <input type="checkbox" className="mt-1 accent-[#b6e1fa]" required />
                  I agree to Neartekpod’s privacy policy.
                </label>
                <label className="flex items-start gap-2">
                  <input type="checkbox" className="mt-1 accent-[#b6e1fa]" required />
                  I agree to receive news and articles from Neartekpod
                </label>
              </div>
              <button type="submit" className="w-fit mt-2 px-8 py-2 border-2 border-black rounded-full font-medium hover:bg-black hover:text-white transition-all">SEND MESSAGE</button>
            </form>
          </div>
        </div>
      </section>

      {/* 🔹 Footer */}
      <FooterSection />
    </div>
  );
}
