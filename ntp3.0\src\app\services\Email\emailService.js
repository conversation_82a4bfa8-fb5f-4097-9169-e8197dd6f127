import emailjs from '@emailjs/browser';

export const sendEmail = async (data) => {
    console.log("Sending email with data:", data);
  const serviceID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID;
  const templateID = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID;
  const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY;
  const templateParams = data;

  return emailjs.send(serviceID, templateID, templateParams, publicKey);
};
