"use client";

import Image from "next/image";
import NavigationHeader from "../../components/navigation-header";
import FooterSection from "../../components/footer-section";
import Link from "next/link";
import { FiArrowRight, FiSettings, <PERSON><PERSON><PERSON><PERSON><PERSON>ck, <PERSON><PERSON>lock } from "react-icons/fi";


const Hospitality = () => {
  return (
    <>
      <NavigationHeader />
      <div
        id="hospitality-hero"
        className="relative w-[1179px] h-[678px] mt-[30px] mx-auto rounded-[14.03px] overflow-hidden shadow-[0_4px_24px_rgba(0,0,0,0.08)]"
      >
        {/* Background Image */}
        <Image
          src="/servicepage/hospitality_subpage.jpg"
          alt="Hospitality Hero"
          fill
          className="object-cover z-[1]"
        />

        {/* Gradient Overlay */}
        <div className="absolute top-0 left-0 w-full h-full z-[2] rounded-[14.03px] bg-gradient-to-b from-[#061C3D0A] via-[#061C3D8F] to-[#061C3DCC]" />

        {/* Hero Content */}
        <div className="absolute top-0 left-0 w-full h-full z-[3] flex items-center justify-end px-12">
          {/* Left Side: Text and Button */}
          <div className="absolute left-12 bottom-12 w-[541px] h-[170px] flex flex-col justify-end">
            <h1 className="text-white text-[40px] leading-[1.1] font-bold font-montserrat mb-6">
              Elevate Your Hospitality Experience with our <br />
              <span className="text-[#0099ff]">
                Oracle OPERA Cloud Solutions
              </span>
            </h1>
            <div>
              <Link
                href=""
                className="inline-flex items-center justify-between mt-6 px-6 py-2 bg-[#0099ff] hover:bg-[#0077cc] text-white font-montserrat font-bold text-base shadow-md transition-all duration-300 rounded-full w-[288px] h-[72px]"
              >
                Contact us
                <span className="ml-3 flex items-center justify-center w-6 h-6 bg-white rounded-full">
                  <FiArrowRight className="text-black text-xl font-bold" />
                </span>
              </Link>
            </div>
          </div>

          {/* Right Side: Feature Cards */}
          <div className="absolute z-10 top-[108px] left-[636.43px] w-[532.74px] h-[475px] grid gap-[16px] [grid-template-columns:repeat(2,_1fr)] [grid-template-rows:repeat(2,_1fr)]">
            {/* Top Left: Empty */}
            <div className="w-[240px] h-[240px]" />

            {/* Top Right Card */}
            <div className="w-[240px] h-[240px] bg-white/30 rounded-xl p-7 text-white shadow-[0_2px_8px_rgba(0,0,0,0.08)] relative flex flex-col justify-center">
              <span className="absolute top-5 right-5 w-10 h-10 flex items-center justify-center bg-white rounded-full shadow-md">
                <FiSettings className="text-[#0099ff] text-2xl" />
              </span>
              <h3 className="text-[20px] font-bold mb-1 font-montserrat">
                Streamlined Operations
              </h3>
              <p className="text-sm font-normal font-montserrat">
                Optimize your hotel's operations: efficiently managing bookings
                to housekeeping
              </p>
            </div>

            {/* Bottom Left Card */}
            <div className="w-[240px] h-[240px] -mt-[95px] -ml-[28px] bg-white/30 rounded-xl p-7 text-white shadow-[0_2px_8px_rgba(0,0,0,0.08)] relative flex flex-col justify-center">
              <span className="absolute top-2 right-5 w-10 h-10 flex items-center justify-center bg-white rounded-full shadow-md">
                <FiUserCheck className="text-[#0099ff] text-2xl" />
              </span>
              <h3 className="text-[20px] font-bold mb-1 font-montserrat">
                Frictionless Guest Experiences
              </h3>
              <p className="text-sm font-normal font-montserrat">
                Deliver seamless, personalized interactions that keep your
                guests coming back.
              </p>
            </div>

            {/* Bottom Right Card */}
            <div className="w-[240px] h-[240px] mt-[50px] bg-white/30 rounded-xl p-7 text-white shadow-[0_2px_8px_rgba(0,0,0,0.08)] relative flex flex-col justify-center">
              <span className="absolute top-5 right-5 w-10 h-10 flex items-center justify-center bg-white rounded-full shadow-md">
                <FiClock className="text-[#0099ff] text-2xl" />
              </span>
              <h3 className="text-[20px] font-bold mb-1 font-montserrat">
                Speed to Market
              </h3>
              <p className="text-sm font-normal font-montserrat">
                Accelerate your time to market with efficient onboarding and
                integration processes.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Move the section header up, right after the hero image, with no extra margin */}
      <div className="w-full max-w-[1189px] h-[281px] mx-auto flex items-center justify-center mt-0 mb-0 pt-0 pb-0">
        <h2 className="font-montserrat font-semibold text-black text-[40px] leading-[50px] text-center w-full m-0">
          We are the trusted Oracle hospitality partner for leading global
          brands
        </h2>
      </div>

      {/* Testimonial Cards */}
      <div className="w-full flex justify-center items-center gap-40 max-w-[1189px] mx-auto mb-8 h-[254px]">
        {/* Card 1 */}
        <div className="flex items-center bg-gradient-to-br from-[#F7FAFC] to-[#EAF6FF] rounded-[32px] shadow-md p-8 w-[500px] h-[220px] border border-[#E0E7EF]">
          <div className="flex-shrink-0 w-[70px] h-[70px] rounded-full bg-white flex items-center justify-center mr-6">
            <img
              src="/servicepage/Hospitality/marriott.png"
              alt="Marriott"
              className="w-12 h-12 object-contain"
            />
          </div>
          <div>
            <div className="font-montserrat font-bold text-black text-[18px] mb-1">
              Highly Recommend!
            </div>
            <div className="font-montserrat text-[16px] text-[#222] mb-2">
              Global team of hospitality specialists made the real difference in
              bringing PMS & Loyalty platforms together
            </div>
            <div className="font-montserrat text-[15px] text-[#444]">
              Director
              <br />
              <span className="font-bold text-black">Marriott</span>
            </div>
          </div>
        </div>

        {/* Card 2 */}
        <div className="flex items-center bg-gradient-to-br from-[#F7FAFC] to-[#EAF6FF] rounded-[32px] shadow-md p-8 w-[500px] h-[220px] border border-[#E0E7EF]">
          <div className="flex-shrink-0 w-[70px] h-[70px] rounded-full bg-white flex items-center justify-center mr-6">
            <img
              src="/servicepage/Hospitality/oracle.png"
              alt="Oracle"
              className="w-12 h-12 object-contain"
            />
          </div>
          <div>
            <div className="font-montserrat font-bold text-black text-[18px] mb-1">
              A proven choice
            </div>
            <div className="font-montserrat text-[16px] text-[#222] mb-2">
              They're an Oracle partner that delivers where even Diamond
              partners fall short.
            </div>
            <div className="font-montserrat text-[15px] text-[#444]">
              Senior Director
              <br />
              <span className="font-bold text-black">Oracle Hospitality</span>
            </div>
          </div>
        </div>
      </div>

      {/* Marquee Brand Logos */}
      <div className="w-full overflow-hidden py-8 bg-transparent">
        <div className="flex items-center whitespace-nowrap animate-marquee">
          {/* Logos - repeated 3 times for seamless loop */}
          {Array(3).fill(
            <>
              <img
                src="/servicepage/Hospitality/marriott.png"
                alt="Marriott"
                className="mx-8 h-12 inline-block"
              />
              <img
                src="/servicepage/Hospitality/mgm.png"
                alt="MGM Grand"
                className="mx-8 h-12 inline-block"
              />
              <img
                src="/servicepage/Hospitality/ihg.png"
                alt="IHG"
                className="mx-8 h-12 inline-block"
              />
              <img
                src="/servicepage/Hospitality/tbc.png"
                alt="TBC Hotels"
                className="mx-8 h-12 inline-block"
              />
              <img
                src="/servicepage/Hospitality/oracle.png"
                alt="Oracle"
                className="mx-8 h-12 inline-block"
              />
            </>
          )}
        </div>
      </div>

      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        .animate-marquee {
          animation: marquee 18s linear infinite;
        }
      `}</style>

      <div className="w-full max-w-[1180px] mx-auto flex flex-col justify-center mt-0 mb-0 pt-0 pb-0">
        <p className="rounded-full px-6 py-2 ml-0 mb-[16px] text-[#00A2FF] font-montserrat font-bold text-[13.19px] leading-[23.08px] tracking-[0.02em] uppercase bg-none inline-block">
          WHY CHOOSE US
        </p>
        <h2 className="font-montserrat font-semibold text-black text-[40px] leading-[50px] text-center w-full m-0">
          We leverage our expertise to deliver successful OPERA Cloud PMS
          solutions. Here's what we bring!
        </h2>
      </div>

      <div className="w-full max-w-[1180px] mx-auto grid grid-cols-2 justify-center items-center gap-x-16 gap-y-16 mt-[92px]">
        {/* Card 1 */}
        <div className="relative flex flex-col items-center bg-white rounded-[24px] shadow-[0_8px_32px_0_rgba(44,62,80,0.08)] w-[566px] h-[307px] mt-[92px] mx-auto pt-8 px-6 pb-6">
          <div className="absolute -translate-x-1/2 top-[-100px] left-[283px] w-[172.99px] h-[172.99px] bg-white rounded-[86.5px] shadow-[0_4px_24px_0_rgba(44,62,80,0.10)] flex items-center justify-center">
            <img
              src="/servicepage/Hospitality/Bars Chart.png"
              alt="Tailored Migrations"
              className="w-[100px] h-[100px]"
            />
          </div>
          <div className="flex flex-col justify-center items-center h-full mt-[100px] text-center">
            <span className="w-[464px] h-[241px] font-montserrat font-semibold text-[30px] leading-[123%] text-[#272727] inline-block rounded-[24px] py-4">
              Tailored Migrations
              <br />
              leveraging the power of OHIP APIs
            </span>
          </div>
        </div>

        {/* Card 2 */}
        <div className="relative flex flex-col items-center bg-white rounded-[24px] shadow-[0_8px_32px_0_rgba(44,62,80,0.08)] w-[566px] h-[307px] mt-[92px] mx-auto pt-8 px-6 pb-6">
          <div className="absolute -translate-x-1/2 top-[-100px] left-[283px] w-[172.99px] h-[172.99px] bg-white rounded-[86.5px] shadow-[0_4px_24px_0_rgba(44,62,80,0.10)] flex items-center justify-center">
            <img
              src="/servicepage/Hospitality/Growth Arrow.svg"
              alt="Tailored Migrations"
              className="w-[100px] h-[100px]"
            />
          </div>
          <div className="flex flex-col justify-center items-center h-full mt-[100px] text-center">
            <span className="w-[464px] h-[241px] font-montserrat font-semibold text-[30px] leading-[123%] text-[#272727] inline-block rounded-[24px] py-4">
              Automated Property Onboarding
            </span>
          </div>
        </div>

        {/* Card 3 */}
        <div className="relative flex flex-col items-center bg-white rounded-[24px] shadow-[0_8px_32px_0_rgba(44,62,80,0.08)] w-[566px] h-[307px] mt-[92px] mx-auto pt-8 px-6 pb-6">
          <div className="absolute -translate-x-1/2 top-[-100px] left-[283px] w-[172.99px] h-[172.99px] bg-white rounded-[86.5px] shadow-[0_4px_24px_0_rgba(44,62,80,0.10)] flex items-center justify-center">
            <img
              src="/servicepage/Hospitality/Color <EMAIL>"
              alt="Tailored Migrations"
              className="w-[100px] h-[100px]"
            />
          </div>
          <div className="flex flex-col justify-center items-center h-full mt-[100px] text-center">
            <span className="w-[464px] h-[241px] font-montserrat font-semibold text-[30px] leading-[123%] text-[#272727] inline-block rounded-[24px] py-4">
              Hospitality AI Solutions
            </span>
          </div>
        </div>

        {/* Card 4 */}
        <div className="relative flex flex-col items-center bg-white rounded-[24px] shadow-[0_8px_32px_0_rgba(44,62,80,0.08)] w-[566px] h-[307px] mt-[92px] mx-auto pt-8 px-6 pb-6">
          <div className="absolute -translate-x-1/2 top-[-100px] left-[283px] w-[172.99px] h-[172.99px] bg-white rounded-[86.5px] shadow-[0_4px_24px_0_rgba(44,62,80,0.10)] flex items-center justify-center">
            <img
              src="/servicepage/Hospitality/comprehensive.png"
              alt="Tailored Migrations"
              className="w-[100px] h-[100px]"
            />
          </div>
          <div className="flex flex-col justify-center items-center h-full mt-[100px] text-center">
            <span className="w-[464px] h-[241px] font-montserrat font-semibold text-[30px] leading-[123%] text-[#272727] inline-block rounded-[24px] py-4">
              Comprehensive Playbook
            </span>
          </div>
        </div>

        <div className="relative w-[1254.99px] h-[712.23px] mx-auto">
          <Image
            src="/servicepage/Hospitality/Frame.svg"
            alt="Background"
            fill
            className="object-cover rounded-[32px] z-0"
          />
        </div>
      </div>

      <FooterSection />
    </>
  );
};

export default Hospitality;
